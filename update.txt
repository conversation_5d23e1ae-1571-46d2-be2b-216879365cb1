import os 
import json
import uuid 
import asyncio
from transformers import AutoTokenizer
from vllm import AsyncEngineArgs, AsyncLLMEngine, SamplingParams
import pandas as pd 
from aiofiles import open as aioopen 
from datetime import datetime 
import logging 
from tqdm import tqdm 
import multiprocessing

multiprocessing.set_start_method("spawn", force=True)

# 配置日志（移到全局）
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("info0717.log"),
        logging.StreamHandler()  # 添加控制台输出
    ]
)
logger = logging.getLogger(__name__)

def jsonify(data: dict) -> str:
    return json.dumps(data, ensure_ascii=False)

async def read_jsonl(file_path: str) -> pd.DataFrame:
    records = []
    async with aioopen(file_path, mode='r', encoding='utf-8') as f:
        async for line in f:
            line = line.strip()
            if line:
                records.append(json.loads(line))
    return pd.DataFrame(records)

tokenizer = AutoTokenizer.from_pretrained("/wugaungxu/model/Qwen-2.5-72b")

engine_args = {
    "model": "/wugaungxu/model/Qwen-2.5-72b",
    "trust_remote_code": True,
    "dtype": "bfloat16",
    "enforce_eager": True,
    "tensor_parallel_size": 8,
    "gpu_memory_utilization": 0.9,
    "max_model_len": 320000
}

llm = AsyncLLMEngine.from_engine_args(AsyncEngineArgs(**engine_args))

async def process_file(file_path: str, checkpoint_file: str, output_file: str):
    checkpoint = {}
    start_index = 0

    if os.path.exists(checkpoint_file):
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as cfile:
                checkpoint = json.load(cfile)
            start_index = checkpoint.get(file_path, 0)
            logger.info(f"从断点恢复：{file_path}[{start_index}]")
        except Exception as e:
            logger.error(f"Failed to load checkpoint file: {e}")
            checkpoint = {file_path: 0}
            with open(checkpoint_file, 'w', encoding='utf-8') as cfile:
                json.dump(checkpoint, cfile)
    else:
        checkpoint = {file_path: 0}
        with open(checkpoint_file, 'w', encoding='utf-8') as cfile:
            json.dump(checkpoint, cfile)

    file_content = await read_jsonl(file_path)
    total_rows = len(file_content)
    logger.info(f"总共需要处理 {total_rows} 行数据，从第 {start_index} 行开始")

    sampling_params = SamplingParams(
        repetition_penalty=1.05,
        temperature=0.3,
        stop_token_ids=[tokenizer.eos_token_id] + tokenizer.additional_special_tokens_ids,
        max_tokens=16000,
        skip_special_tokens=True
    )

    system_prompt = """
        etc...
    """
    
    process_bar = tqdm(total=total_rows, initial=start_index, desc="processing...")
    processed_count = 0

    try:
        # 修正索引问题
        for i, (idx, row) in enumerate(file_content.iloc[start_index:].iterrows()):
            current_index = start_index + i
            try:
                full_prompt = system_prompt.format(content=row['text'])  # 修正格式化
                messages = [
                    {"role": "system", "content": "You are Qwen"},
                    {"role": "user", "content": full_prompt}
                ]

                text = tokenizer.apply_chat_template(
                    messages,
                    tokenize=False,
                    add_generation_prompt=True
                )
                
                request_id = f"chatcmpl-{uuid.uuid4().hex}"
                result_generator = llm.generate(
                    text,
                    sampling_params=sampling_params,
                    request_id=request_id,
                    lora_request=None
                )
                
                generated_text = ""
                async for result in result_generator:
                    generated_text = result.outputs[0].text

                # 创建新的记录而不是修改原始row
                output_record = row.to_dict()
                output_record['response'] = generated_text
                output_record['processed_at'] = datetime.now().isoformat()

                # 修正json写入
                with open(output_file, "a", encoding="utf-8") as outfile:
                    json_record = json.dumps(output_record, ensure_ascii=False)
                    outfile.write(json_record + '\n')

                processed_count += 1
                process_bar.update(1)

                # 修正检查点保存条件
                if processed_count % 10 == 0:
                    checkpoint[file_path] = current_index + 1
                    with open(checkpoint_file, 'w', encoding='utf-8') as f:
                        json.dump(checkpoint, f)
                    logger.info(f"Checkpoint saved at {current_index + 1}")
                    
            except Exception as e:
                error_msg = f"处理第 {current_index} 行失败: {str(e)}"
                logger.error(error_msg, exc_info=True)
                
                checkpoint[file_path] = current_index
                with open(checkpoint_file, 'w', encoding='utf-8') as f:
                    json.dump(checkpoint, f)
                logger.warning(f"保存到断点行 {current_index}")
                
    except Exception as e:
        error_msg = f"处理中断于第 {current_index} 行: {str(e)}"
        logger.critical(error_msg, exc_info=True)
        
        checkpoint[file_path] = current_index
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint, f)
        raise e
    
    finally:
        process_bar.close()
        # 保存最终检查点
        checkpoint[file_path] = total_rows
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(checkpoint, f)
        logger.info("处理完成!")
    
    logger.info(f"处理完成 {file_path}，共处理 {processed_count} 行数据")

async def main():
    file_path = "a.jsonl"
    checkpoint_file = "b.jsonl"
    output_file = "c.jsonl"
    await process_file(file_path, checkpoint_file, output_file)

if __name__ == "__main__":
    asyncio.run(main())