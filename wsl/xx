import os 
import json
import uuid 
import asyncio
from transformers import AutoTokenizer
from vllm import AsyncEngineArgs, AsyncLLMEngine, SamplingParams
import pandas as pd 
from aiofiles import open as aioopen 
from datetime import datetime 
import logging 
from tqdm import tqdm 
import multiprocessing

multiprocessing.set_start_method("spawn", force=True)

def jsonify(data:dict) -> str:
    return json.dumps(data,ensure_ascii=False)

async def read_jsonl(file_path:str) -> pd.DataFrame:
    records = []
    async with aioopen(file_path,mode='r',encoding='utf-8') as f:
        async for line in f:
            line = line.strip()
            if line:
                records.append(json.loads(line))
    return pd.DataFrame(records)

tokenizer = AutoTokenizer.form_pretrained("/wugaungxu/model/Qwen-2.5-72b")

engine_args = {
    "model":"/wugaungxu/model/Qwen-2.5-72b",
    "trust_remote_code":True,
    "dtype":"bfloat16",
    "enforce_eager":True,
    "tensor_parallel_size":8,
    "gpu_memory_utilization":0.9,
    "max_model_len":320000
}

llm = AsyncLLMEngine.from_engine_args(AsyncEngineArgs(**engine_args))

async def process_file(file_path:str, checkpoint_file, output_file):
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
        handlers=[
            logging.FileHandler("info0717.log")
        ]
    )
    logger = logging.getLogger()

    checkpoint = {}
    start_index = 0

    if os.path.exists(checkpoint_file):
        try:
            with open (checkpoint_file,'r',encoding='utf-8') as cfile:
                checkpoint = json.load(cfile)

            start_index = checkpoint.get(file_path,0)
            current_time = datetime.now().strftime("%Y-%M-%d %H:%M:%S")
            logging.info(f"从断点恢复：{file_path}[{start_index}]")
        except Exception as e:
            logging.error(f"Failed to load checkpoint file: {e}")
            checkpoint = {file_path: 0}
            with open(checkpoint_file,'w',encoding='utf-8') as cfile:
                json.dump(checkpoint, cfile)
        
    else:
        checkpoint = {file_path:0}
        with open (checkpoint_file,'w',encoding='utf-8') as cfile:
            json.dump(checkpoint,cfile)

    file_content = await read_jsonl(file_path)
    total_rows = len(file_content)

    sampling_params = SamplingParams(
        repetition_penalty = 1.05,
        temperature =0.3
        stop_token_ids = [tokenizer.eos_token_id] + tokenizer.additional_special_tokens_ids,
        max_tokens = 16000,
        skip_special_tokens = True
    )

    system_prompt = """
        etc...
    """
    process_bar = tqdm(total=total_rows, initial = start_index, desc = "processing..." )
    processed_count = 0

    try:
        for idx, row in file_content.iloc[start_index:].iterrows():
            current_index = idx +1 
            try:
                full_prompt = system_prompt.format(content=['text'])
                messages =[
                    {"role":"system","content":"You are Qwen"}
                    {"role":"user", "content":full_prompt}
                ]

                text = tokenizer.apply_chat_template(
                    messages,
                    tokenize=False,
                    add_generation_prompt=True
                )
                request_id = "chatcmpl-{}".format(uuid.uuid4().hex)
                result_generator = llm.generate(
                    text,
                    sampling_params = sampling_params,
                    request_id=request_id,
                    lora_request=None
                )
                generated_text = ""
                async for result in result_generator:
                    generated_text = result.outputs[0].text

                row['response'] = generated_text
                row['processed_at'] = datetime.now().isoformat()

                with open (output_file, "a", encoding="utf-8") as outfile:
                    json_record = json.dump(row.to_dict(),ensure_ascii=False)
                    outfile.write(json_record + '\n')

                processed_count += 1 
                logging.info(f"checkpoint undated {current_index}")
                process_bar.update(1)

                if process_bar % 10 == 0:
                    checkpoint[file_path] = current_index
                    with open (checkpoint_file,'w') as f:
                        json.dump(checkpoint, f)
                    logging.info(f"Checkpoint saved")
            except Exception as e :
                error_msg = f"失败 {current_index} : {str(e)}"
                logging.error(error_msg, exc_info=True)

                checkpoint[file_path] = current_index - 1
                logging.info(f"checkpoint updated {current_index}")
                with open (checkpoint_file,'w') as f:
                    json.dump(checkpoint, f)
                logging.warning(f"保存到断点行{current_index - 1}由于错误")
    except Exception as e :
        error_msg = f"处理中断于 {current_index} : {str(e)}"
        logging.critical(error_msg,exc_info=True)

        checkpoint[file_path] = current_index - 1 if current_index > 0 else 0
        with open (checkpoint_file,'w') as f:
            json.dump(checkpoint, f)
        raise e
    
    finally:
        process_bar.close()
        with open (checkpoint_file,'w') as f:
            json.dump(checkpoint, f)
        logging.info("complete!")
    
    current_time = datetime.now().strftime("%Y-%M-%d %H:%M:%S")
    logging.info(f"prcessing completed for {file_path},{processed_count} rows processed")

    async def main():
        file_path = "a.jsonl"
        checkpoint_file = "b.jsonl"
        output_file = "c.jsonl"
        await process_file(file_path,checkpoint_file, output_file)
    
    await.main()