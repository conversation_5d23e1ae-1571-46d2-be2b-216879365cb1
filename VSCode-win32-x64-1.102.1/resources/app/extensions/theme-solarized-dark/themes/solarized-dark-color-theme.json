{"name": "Solarized (dark)", "tokenColors": [{"settings": {"foreground": "#839496"}}, {"scope": ["meta.embedded", "source.groovy.embedded", "string meta.image.inline.markdown", "variable.legacy.builtin.python"], "settings": {"foreground": "#839496"}}, {"name": "Comment", "scope": "comment", "settings": {"fontStyle": "italic", "foreground": "#586E75"}}, {"name": "String", "scope": "string", "settings": {"foreground": "#2AA198"}}, {"name": "Regexp", "scope": "string.regexp", "settings": {"foreground": "#DC322F"}}, {"name": "Number", "scope": "constant.numeric", "settings": {"foreground": "#D33682"}}, {"name": "Variable", "scope": ["variable.language", "variable.other"], "settings": {"foreground": "#268BD2"}}, {"name": "Keyword", "scope": "keyword", "settings": {"foreground": "#859900"}}, {"name": "Storage", "scope": "storage", "settings": {"fontStyle": "bold", "foreground": "#93A1A1"}}, {"name": "Class name", "scope": ["entity.name.class", "entity.name.type", "entity.name.namespace", "entity.name.scope-resolution"], "settings": {"fontStyle": "", "foreground": "#CB4B16"}}, {"name": "Function name", "scope": "entity.name.function", "settings": {"foreground": "#268BD2"}}, {"name": "Variable start", "scope": "punctuation.definition.variable", "settings": {"foreground": "#859900"}}, {"name": "Embedded code markers", "scope": ["punctuation.section.embedded.begin", "punctuation.section.embedded.end"], "settings": {"foreground": "#DC322F"}}, {"name": "Built-in constant", "scope": ["constant.language", "meta.preprocessor"], "settings": {"foreground": "#B58900"}}, {"name": "Support.construct", "scope": ["support.function.construct", "keyword.other.new"], "settings": {"foreground": "#CB4B16"}}, {"name": "User-defined constant", "scope": ["constant.character", "constant.other"], "settings": {"foreground": "#CB4B16"}}, {"name": "Inherited class", "scope": ["entity.other.inherited-class", "punctuation.separator.namespace.ruby"], "settings": {"foreground": "#6C71C4"}}, {"name": "Function argument", "scope": "variable.parameter", "settings": {}}, {"name": "Tag name", "scope": "entity.name.tag", "settings": {"foreground": "#268BD2"}}, {"name": "Tag start/end", "scope": "punctuation.definition.tag", "settings": {"foreground": "#586E75"}}, {"name": "Tag attribute", "scope": "entity.other.attribute-name", "settings": {"foreground": "#93A1A1"}}, {"name": "Library function", "scope": "support.function", "settings": {"foreground": "#268BD2"}}, {"name": "Continuation", "scope": "punctuation.separator.continuation", "settings": {"foreground": "#DC322F"}}, {"name": "Library constant", "scope": ["support.constant", "support.variable"], "settings": {}}, {"name": "Library class/type", "scope": ["support.type", "support.class"], "settings": {"foreground": "#859900"}}, {"name": "Library Exception", "scope": "support.type.exception", "settings": {"foreground": "#CB4B16"}}, {"name": "Library variable", "scope": "support.other.variable", "settings": {}}, {"name": "Invalid", "scope": "invalid", "settings": {"foreground": "#DC322F"}}, {"name": "diff: header", "scope": ["meta.diff", "meta.diff.header"], "settings": {"fontStyle": "italic", "foreground": "#268BD2"}}, {"name": "diff: deleted", "scope": "markup.deleted", "settings": {"fontStyle": "", "foreground": "#DC322F"}}, {"name": "diff: changed", "scope": "markup.changed", "settings": {"fontStyle": "", "foreground": "#CB4B16"}}, {"name": "diff: inserted", "scope": "markup.inserted", "settings": {"foreground": "#859900"}}, {"name": "<PERSON><PERSON> Quote", "scope": "markup.quote", "settings": {"foreground": "#859900"}}, {"name": "Markup Lists", "scope": "markup.list", "settings": {"foreground": "#B58900"}}, {"name": "<PERSON><PERSON>", "scope": ["markup.bold", "markup.italic"], "settings": {"foreground": "#D33682"}}, {"name": "Markup: <PERSON>", "scope": "markup.bold", "settings": {"fontStyle": "bold"}}, {"name": "Markup: Emphasis", "scope": "markup.italic", "settings": {"fontStyle": "italic"}}, {"scope": "markup.strikethrough", "settings": {"fontStyle": "strikethrough"}}, {"name": "Markup Inline", "scope": "markup.inline.raw", "settings": {"fontStyle": "", "foreground": "#2AA198"}}, {"name": "<PERSON><PERSON>", "scope": "markup.heading", "settings": {"fontStyle": "bold", "foreground": "#268BD2"}}, {"name": "<PERSON><PERSON> Header", "scope": "markup.heading.setext", "settings": {"fontStyle": "", "foreground": "#268BD2"}}], "colors": {"focusBorder": "#2AA19899", "selection.background": "#2AA19899", "input.background": "#003847", "input.foreground": "#93A1A1", "input.placeholderForeground": "#93A1A1AA", "inputOption.activeBorder": "#2AA19899", "inputValidation.infoBorder": "#363b5f", "inputValidation.infoBackground": "#052730", "inputValidation.warningBackground": "#5d5938", "inputValidation.warningBorder": "#9d8a5e", "inputValidation.errorBackground": "#571b26", "inputValidation.errorBorder": "#a92049", "errorForeground": "#ffeaea", "badge.background": "#047aa6", "progressBar.background": "#047aa6", "dropdown.background": "#00212B", "dropdown.border": "#2AA19899", "button.background": "#2AA19899", "list.activeSelectionBackground": "#005A6F", "quickInputList.focusBackground": "#005A6F", "list.hoverBackground": "#004454AA", "list.inactiveSelectionBackground": "#00445488", "list.dropBackground": "#00445488", "list.highlightForeground": "#1ebcc5", "editor.background": "#002B36", "editor.foreground": "#839496", "editorWidget.background": "#00212B", "editorCursor.foreground": "#D30102", "editorWhitespace.foreground": "#93A1A180", "editor.lineHighlightBackground": "#073642", "editorLineNumber.activeForeground": "#949494", "editor.selectionBackground": "#274642", "minimap.selectionHighlight": "#274642", "editorIndentGuide.background": "#93A1A180", "editorIndentGuide.activeBackground": "#C3E1E180", "editorHoverWidget.background": "#004052", "editorMarkerNavigationError.background": "#AB395B", "editorMarkerNavigationWarning.background": "#5B7E7A", "editor.selectionHighlightBackground": "#005A6FAA", "editor.wordHighlightBackground": "#004454AA", "editor.wordHighlightStrongBackground": "#005A6FAA", "editorBracketHighlight.foreground1": "#cdcdcdff", "editorBracketHighlight.foreground2": "#b58900ff", "editorBracketHighlight.foreground3": "#d33682ff", "peekViewResult.background": "#00212B", "peekViewEditor.background": "#10192c", "peekViewTitle.background": "#00212B", "peekView.border": "#2b2b4a", "peekViewEditor.matchHighlightBackground": "#7744AA40", "titleBar.activeBackground": "#002C39", "editorGroup.border": "#00212B", "editorGroup.dropBackground": "#2AA19844", "editorGroupHeader.tabsBackground": "#004052", "tab.activeForeground": "#d6dbdb", "tab.activeBackground": "#002B37", "tab.inactiveForeground": "#93A1A1", "tab.inactiveBackground": "#004052", "tab.border": "#003847", "tab.lastPinnedBorder": "#2AA19844", "activityBar.background": "#003847", "panel.border": "#2b2b4a", "sideBar.background": "#00212B", "sideBarTitle.foreground": "#93A1A1", "statusBar.foreground": "#93A1A1", "statusBar.background": "#00212B", "statusBar.debuggingBackground": "#00212B", "statusBar.noFolderBackground": "#00212B", "statusBarItem.remoteBackground": "#2AA19899", "ports.iconRunningProcessForeground": "#369432", "statusBarItem.prominentBackground": "#003847", "statusBarItem.prominentHoverBackground": "#003847", "debugToolBar.background": "#00212B", "debugExceptionWidget.background": "#00212B", "debugExceptionWidget.border": "#AB395B", "pickerGroup.foreground": "#2AA19899", "pickerGroup.border": "#2AA19899", "terminal.ansiBlack": "#073642", "terminal.ansiRed": "#dc322f", "terminal.ansiGreen": "#859900", "terminal.ansiYellow": "#b58900", "terminal.ansiBlue": "#268bd2", "terminal.ansiMagenta": "#d33682", "terminal.ansiCyan": "#2aa198", "terminal.ansiWhite": "#eee8d5", "terminal.ansiBrightBlack": "#002b36", "terminal.ansiBrightRed": "#cb4b16", "terminal.ansiBrightGreen": "#586e75", "terminal.ansiBrightYellow": "#657b83", "terminal.ansiBrightBlue": "#839496", "terminal.ansiBrightMagenta": "#6c71c4", "terminal.ansiBrightCyan": "#93a1a1", "terminal.ansiBrightWhite": "#fdf6e3"}, "semanticHighlighting": true}