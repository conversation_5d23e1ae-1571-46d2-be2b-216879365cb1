{"information_for_contributors": ["This file has been converted from https://github.com/RedCMD/YAML-Syntax-Highlighter/blob/master/syntaxes/yaml-1.1.tmLanguage.json", "If you want to provide a fix or improvement, please create a pull request against the original repository.", "Once accepted there, we are happy to receive an update request."], "version": "https://github.com/RedCMD/YAML-Syntax-Highlighter/commit/dfd7e5f4f71f9695c5d8697ca57f81240165aa04", "name": "YAML 1.1", "scopeName": "source.yaml.1.1", "comment": "https://yaml.org/spec/1.1/", "patterns": [{"include": "#stream"}], "repository": {"stream": {"patterns": [{"comment": "allows me to just use `\\G` instead of the performance heavy `(^|\\G)`", "begin": "^(?!\\G)", "while": "^", "name": "meta.stream.yaml", "patterns": [{"include": "#byte-order-mark"}, {"include": "#directives"}, {"include": "#document"}, {"include": "#presentation-detail"}]}, {"begin": "\\G", "while": "\\G", "name": "meta.stream.yaml", "patterns": [{"include": "#byte-order-mark"}, {"include": "#directives"}, {"include": "#document"}, {"include": "#presentation-detail"}]}]}, "directive-YAML": {"comment": "https://yaml.org/spec/1.2.2/#681-yaml-directives", "begin": "(?=%YAML[ \t]+1\\.1(?=[\\x{85 2028 2029}\r\n\t ]))", "end": "\\G(?=%(?!YAML[ \t]+1\\.1))", "name": "meta.1.1.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#681-yaml-directives", "begin": "\\G(%)(YAML)([ \t]+)(1\\.1)", "while": "\\G(?!---[\\x{85 2028 2029}\r\n\t ])", "beginCaptures": {"1": {"name": "punctuation.definition.directive.begin.yaml"}, "2": {"name": "keyword.other.directive.yaml.yaml"}, "3": {"name": "punctuation.whitespace.separator.yaml"}, "4": {"name": "constant.numeric.yaml-version.yaml"}}, "name": "meta.directives.yaml", "patterns": [{"include": "#directive-invalid"}, {"include": "#directives"}, {"include": "#presentation-detail"}]}, {"begin": "\\G(?=---[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?!%)", "patterns": [{"include": "#document"}, {"include": "#presentation-detail"}]}, {"include": "#presentation-detail"}]}, "directives": {"comment": "https://yaml.org/spec/1.2.2/#68-directives", "patterns": [{"include": "source.yaml.1.3#directive-YAML"}, {"include": "source.yaml.1.2#directive-YAML"}, {"include": "source.yaml.1.1#directive-YAML"}, {"include": "source.yaml.1.0#directive-YAML"}, {"begin": "(?=%)", "while": "\\G(?!%|---[\\x{85 2028 2029}\r\n\t ])", "name": "meta.directives.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#682-tag-directives", "begin": "\\G(%)(TAG)(?>([\t ]++)((!)(?>[0-9A-Za-z-]*+(!))?+))?+", "end": "$", "applyEndPatternLast": true, "beginCaptures": {"1": {"name": "punctuation.definition.directive.begin.yaml"}, "2": {"name": "keyword.other.directive.tag.yaml"}, "3": {"name": "punctuation.whitespace.separator.yaml"}, "4": {"name": "storage.type.tag-handle.yaml"}, "5": {"name": "punctuation.definition.tag.begin.yaml"}, "6": {"name": "punctuation.definition.tag.end.yaml"}, "comment": "https://yaml.org/spec/1.2.2/#rule-c-tag-handle"}, "patterns": [{"comment": "technically the beginning should only validate against a valid uri scheme [A-Za-z][A-Za-z0-9.+-]*", "begin": "\\G[\t ]++(?!#)", "end": "(?=[\\x{85 2028 2029}\r\n\t ])", "beginCaptures": {"0": {"name": "punctuation.whitespace.separator.yaml"}}, "contentName": "support.type.tag-prefix.yaml", "patterns": [{"match": "%[0-9a-fA-F]{2}", "name": "constant.character.escape.unicode.8-bit.yaml"}, {"match": "%[^\\x{85 2028 2029}\r\n\t ]{2,0}", "name": "invalid.illegal.constant.character.escape.unicode.8-bit.yaml"}, {"match": "\\G[,\\[\\]{}]", "name": "invalid.illegal.character.uri.yaml"}, {"include": "source.yaml#non-printable"}, {"match": "[^\\x{85 2028 2029}\r\n\t a-zA-Z0-9-#;/?:@&=+$,_.!~*'()\\[\\]]++", "name": "invalid.illegal.unrecognized.yaml"}]}, {"include": "#presentation-detail"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-reserved-directive", "begin": "(%)([^ \\p{Cntrl}\\p{Surrogate}\\x{2028 2029 FFFE FFFF}]++)", "end": "$", "beginCaptures": {"1": {"name": "punctuation.definition.directive.begin.yaml"}, "2": {"name": "keyword.other.directive.other.yaml"}}, "patterns": [{"match": "\\G([\t ]++)([^ \\p{Cntrl}\\p{Surrogate}\\x{2028 2029 FFFE FFFF}]++)", "captures": {"1": {"name": "punctuation.whitespace.separator.yaml"}, "2": {"name": "string.unquoted.directive-name.yaml"}}}, {"match": "([\t ]++)([^ \\p{Cntrl}\\p{Surrogate}\\x{2028 2029 FFFE FFFF}]++)", "captures": {"1": {"name": "punctuation.whitespace.separator.yaml"}, "2": {"name": "string.unquoted.directive-parameter.yaml"}}}, {"include": "#presentation-detail"}]}, {"match": "\\G\\.{3}(?=[\\x{85 2028 2029}\r\n\t ])", "name": "invalid.illegal.entity.other.document.end.yaml"}, {"include": "#presentation-detail"}]}]}, "directive-invalid": {"patterns": [{"match": "\\G\\.{3}(?=[\\x{85 2028 2029}\r\n\t ])", "name": "invalid.illegal.entity.other.document.end.yaml"}, {"begin": "\\G(%)(YAML)", "end": "$", "beginCaptures": {"1": {"name": "punctuation.definition.directive.begin.yaml"}, "2": {"name": "invalid.illegal.keyword.other.directive.yaml.yaml"}}, "name": "meta.directive.yaml", "patterns": [{"match": "\\G([\t ]++|:)([0-9]++\\.[0-9]++)?+", "captures": {"1": {"name": "punctuation.whitespace.separator.yaml"}, "2": {"name": "constant.numeric.yaml-version.yaml"}}}, {"include": "#presentation-detail"}]}]}, "document": {"comment": "https://yaml.org/spec/1.2.2/#91-documents", "patterns": [{"begin": "---(?=[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?!(?>\\.{3}|---)[\\x{85 2028 2029}\r\n\t ])", "beginCaptures": {"0": {"name": "entity.other.document.begin.yaml"}}, "name": "meta.document.yaml", "patterns": [{"include": "#block-node"}]}, {"begin": "(?=\\.{3}[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?=[\t \\x{FEFF}]*+(?>#|$))", "patterns": [{"begin": "\\G\\.{3}", "end": "$", "beginCaptures": {"0": {"name": "entity.other.document.end.yaml"}}, "patterns": [{"include": "#presentation-detail"}]}, {"include": "#byte-order-mark"}, {"include": "#presentation-detail"}]}, {"begin": "\\G(?!%|[\t \\x{FEFF}]*+(?>#|$))", "while": "\\G(?!(?>\\.{3}|---)[\\x{85 2028 2029}\r\n\t ])", "name": "meta.document.yaml", "patterns": [{"include": "#block-node"}]}]}, "block-node": {"patterns": [{"include": "#block-sequence"}, {"include": "#block-mapping"}, {"include": "source.yaml.1.2#block-scalar"}, {"include": "#anchor-property"}, {"include": "#tag-property"}, {"include": "#alias"}, {"begin": "(?=\"|')", "while": "\\G", "patterns": [{"begin": "(?!\\G)", "while": "\\G", "patterns": [{"include": "#presentation-detail"}]}, {"include": "#double"}, {"include": "#single"}]}, {"begin": "(?={)", "end": "$", "patterns": [{"include": "#flow-mapping"}, {"include": "#presentation-detail"}]}, {"begin": "(?=\\[)", "end": "$", "patterns": [{"include": "#flow-sequence"}, {"include": "#presentation-detail"}]}, {"include": "#block-plain-out"}, {"include": "#presentation-detail"}]}, "block-mapping": {"//": "The check for plain keys is expensive", "begin": "(?=((?<=[-?:]) )?+)(?<![^\t ][\t ]*+:|---)\\G( *+)([\t ]*+)((?>[!&*][^\\x{85 2028 2029}\r\n\t ]*+[\t ]++)*+)(?=(?>(?#Double Quote)\"(?>[^\\\\\"]++|\\\\.)*+\"|(?#Single Quote)'(?>[^']++|'')*+'|(?#Flow-Map){(?>[^\\x{85 2028 2029}}]++|}[ \t]*+(?!:[\\x{85 2028 2029}\r\n\t ]))++}|(?#Flow-Seq)\\[(?>[^\\x{85 2028 2029}\\]]++|][ \t]*+(?!:[\\x{85 2028 2029}\r\n\t ]))++]|(?#Plain)(?>[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ]))(?>[^:#]++|:(?![\\x{85 2028 2029}\r\n\t ])|(?<! |\t)#++)*+)?+(?#Map Value)[\t ]*+:[\\x{85 2028 2029}\r\n\t ]|(?#Explicit)\\?[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?>(\\1\\2)((?>[!&*][^\\x{85 2028 2029}\r\n\t ]*+[\t ]++)*+)((?>\t[\t ]*+)?+[^\\x{85 2028 2029}\r\n\t ?:\\-#!&*\"'\\[\\]{}0-9A-Za-z$()+./;<=\\\\^_~\\x{A0}-\\x{D7FF}\\x{E000}-\\x{FFFD}\\x{010000}-\\x{10FFFF}])?+|( *+)([\t ]*+[^\\x{85 2028 2029}\r\n#])?+)", "beginCaptures": {"2": {"name": "punctuation.whitespace.indentation.yaml"}, "3": {"name": "punctuation.whitespace.separator.yaml"}, "4": {"comment": "May cause lag on long lines starting with a tag, anchor or alias", "patterns": [{"include": "#tag-property"}, {"include": "#anchor-property"}, {"include": "#alias"}, {"include": "#presentation-detail"}]}}, "whileCaptures": {"1": {"name": "punctuation.whitespace.indentation.yaml"}, "2": {"comment": "May cause lag on long lines starting with a tag, anchor or alias", "patterns": [{"include": "#tag-property"}, {"include": "#anchor-property"}, {"include": "#alias"}, {"include": "#presentation-detail"}]}, "3": {"name": "invalid.illegal.expected-indentation.yaml"}, "4": {"name": "punctuation.whitespace.indentation.yaml"}, "5": {"name": "invalid.illegal.expected-indentation.yaml"}}, "name": "meta.mapping.yaml", "patterns": [{"include": "#block-map-key-double"}, {"include": "source.yaml#block-map-key-single"}, {"include": "#block-map-key-plain"}, {"include": "#block-map-key-explicit"}, {"include": "#block-map-value"}, {"include": "#flow-mapping"}, {"include": "#flow-sequence"}, {"include": "#presentation-detail"}]}, "block-sequence": {"comment": "https://yaml.org/spec/1.2.2/#rule-l+block-sequence", "begin": "(?=((?<=[-?:]) )?+)(?<![^\t ][\t ]*+:|---)\\G( *+)(-)(?=[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?>(\\1\\2)(?!-[\\x{85 2028 2029}\r\n\t ])((?>\t[\t ]*+)?+[^\\x{85 2028 2029}\r\n\t #\\]}])?+|(?!\\1\\2)( *+)([\t ]*+[^\\x{85 2028 2029}\r\n#])?+)", "beginCaptures": {"2": {"name": "punctuation.whitespace.indentation.yaml"}, "3": {"name": "punctuation.definition.block.sequence.item.yaml"}}, "whileCaptures": {"1": {"name": "punctuation.whitespace.indentation.yaml"}, "2": {"name": "invalid.illegal.expected-indentation.yaml"}, "3": {"name": "punctuation.whitespace.indentation.yaml"}, "4": {"name": "invalid.illegal.expected-indentation.yaml"}}, "name": "meta.block.sequence.yaml", "patterns": [{"include": "#block-node"}]}, "block-map-key-explicit": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-l-block-map-explicit-key", "begin": "(?=((?<=[-?:]) )?+)\\G( *+)(\\?)(?=[\\x{85 2028 2029}\r\n\t ])", "while": "\\G(?>(\\1\\2)(?![?:0-9A-Za-z$()+./;<=\\\\^_~\\x{A0}-\\x{D7FF}\\x{E000}-\\x{FFFD}\\x{010000}-\\x{10FFFF}&&[^\\x{2028 2029}]])((?>\t[\t ]*+)?+[^\\x{85 2028 2029}\r\n\t #\\-\\[\\]{}])?+|(?!\\1\\2)( *+)([\t ]*+[^\\x{85 2028 2029}\r\n#])?+)", "beginCaptures": {"2": {"name": "punctuation.whitespace.indentation.yaml"}, "3": {"name": "punctuation.definition.map.key.yaml"}, "4": {"name": "punctuation.whitespace.separator.yaml"}}, "whileCaptures": {"1": {"name": "punctuation.whitespace.indentation.yaml"}, "2": {"name": "invalid.illegal.expected-indentation.yaml"}, "3": {"name": "punctuation.whitespace.indentation.yaml"}, "4": {"name": "invalid.illegal.expected-indentation.yaml"}}, "name": "meta.map.explicit.yaml", "patterns": [{"include": "#key-double"}, {"include": "source.yaml#key-single"}, {"include": "#flow-key-plain-out"}, {"include": "#block-map-value"}, {"include": "#block-node"}]}, "block-map-key-double": {"comment": "https://yaml.org/spec/1.2.2/#double-quoted-style (BLOCK-KEY)", "begin": "\\G\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.yaml"}}, "name": "meta.map.key.yaml string.quoted.double.yaml entity.name.tag.yaml", "patterns": [{"match": ".[\t ]*+$", "name": "invalid.illegal.multiline-key.yaml"}, {"match": "[^\t -\\x{10FFFF}]++", "name": "invalid.illegal.character.yaml"}, {"include": "#double-escape"}]}, "block-map-key-plain": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-plain-one-line (BLOCK-KEY)", "begin": "\\G(?=[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ]))", "end": "(?=[\t ]*+:[\\x{85 2028 2029}\r\n\t ]|(?>[\t ]++|\\G)#)", "name": "meta.map.key.yaml string.unquoted.plain.yaml entity.name.tag.yaml", "patterns": [{"include": "#tag-implicit-plain-out"}, {"match": "\\G([\t ]++)(.)", "captures": {"1": {"name": "punctuation.whitespace.separator.yaml"}, "2": {"name": "invalid.illegal.multiline-key.yaml"}}}, {"match": "[\t ]++$", "name": "punctuation.whitespace.separator.yaml"}, {"include": "source.yaml#non-printable"}]}, "block-map-value": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-l-block-map-implicit-value", "//": "Assumming 3rd party preprocessing variables `{{...}}` turn into valid map-keys when inside a block-mapping", "begin": ":(?=[\\x{85 2028 2029}\r\n\t ])|(?<=}})(?=[\t ]++#|[\t ]*+$)", "while": "\\G(?![?:!\"'0-9A-Za-z$()+./;<=\\\\^_~\\[{\\x{A0}-\\x{D7FF}\\x{E000}-\\x{FFFD}\\x{010000}-\\x{10FFFF}&&[^\\x{2028 2029}]]|-[^\\x{85 2028 2029}\r\n\t ])", "beginCaptures": {"0": {"name": "punctuation.separator.map.value.yaml"}}, "name": "meta.map.value.yaml", "patterns": [{"include": "#block-node"}]}, "block-plain-out": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-plain-multi-line (FLOW-OUT)", "begin": "(?=[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ]))", "while": "\\G", "patterns": [{"begin": "\\G", "end": "(?=(?>[\t ]++|\\G)#)", "name": "string.unquoted.plain.out.yaml", "patterns": [{"include": "#tag-implicit-plain-out"}, {"match": ":(?=[\\x{85 2028 2029}\r\n\t ])", "name": "invalid.illegal.multiline-key.yaml"}, {"match": "\\G[\t ]++", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[\t ]++$", "name": "punctuation.whitespace.separator.yaml"}, {"include": "source.yaml#non-printable"}]}, {"begin": "(?!\\G)", "while": "\\G", "patterns": [{"include": "#presentation-detail"}]}]}, "flow-node": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-seq-entry (FLOW-IN)", "patterns": [{"begin": "(?=\\[|{)", "end": "(?=[:,\\]}])", "patterns": [{"begin": "(?!\\G)", "end": "(?=[:,\\]}])", "patterns": [{"include": "#presentation-detail"}]}, {"include": "#flow-mapping"}, {"include": "#flow-sequence"}]}, {"include": "#anchor-property"}, {"include": "#tag-property"}, {"include": "#alias"}, {"begin": "(?=\"|')", "end": "(?=[:,\\]}])", "patterns": [{"begin": "(?!\\G)", "end": "(?=[:,\\]}])", "patterns": [{"include": "#presentation-detail"}]}, {"include": "#double"}, {"include": "#single"}]}, {"include": "#flow-plain-in"}, {"include": "#presentation-detail"}]}, "flow-mapping": {"comment": "https://yaml.org/spec/1.2.2/#742-flow-mappings", "begin": "{", "end": "}", "beginCaptures": {"0": {"name": "punctuation.definition.mapping.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.mapping.end.yaml"}}, "name": "meta.flow.mapping.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-ns-s-flow-map-entries", "begin": "(?<={)\\G(?=[\\x{85 2028 2029}\r\n\t ,#])|,", "end": "(?=[^\\x{85 2028 2029}\r\n\t ,#])", "beginCaptures": {"0": {"name": "punctuation.separator.mapping.yaml"}}, "patterns": [{"match": ",++", "name": "invalid.illegal.separator.sequence.yaml"}, {"include": "#presentation-detail"}]}, {"include": "#flow-mapping-map-key"}, {"include": "#flow-map-value-yaml"}, {"include": "#flow-map-value-json"}, {"include": "#flow-node"}]}, "flow-sequence": {"comment": "https://yaml.org/spec/1.2.2/#741-flow-sequences", "begin": "\\[", "end": "]", "beginCaptures": {"0": {"name": "punctuation.definition.sequence.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.sequence.end.yaml"}}, "name": "meta.flow.sequence.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-ns-s-flow-seq-entries", "begin": "(?<=\\[)\\G(?=[\\x{85 2028 2029}\r\n\t ,#])|,", "end": "(?=[^\\x{85 2028 2029}\r\n\t ,#])", "beginCaptures": {"0": {"name": "punctuation.separator.sequence.yaml"}}, "patterns": [{"match": ",++", "name": "invalid.illegal.separator.sequence.yaml"}, {"include": "#presentation-detail"}]}, {"include": "#flow-sequence-map-key"}, {"include": "#flow-map-value-yaml"}, {"include": "#flow-map-value-json"}, {"include": "#flow-node"}]}, "flow-mapping-map-key": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-entry (FLOW-IN)", "patterns": [{"begin": "\\?(?=[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "end": "(?=[,\\[\\]{}])", "beginCaptures": {"0": {"name": "punctuation.definition.map.key.yaml"}}, "name": "meta.flow.map.explicit.yaml", "patterns": [{"include": "#flow-mapping-map-key"}, {"include": "#flow-map-value-yaml"}, {"include": "#flow-map-value-json"}, {"include": "#flow-node"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-implicit-entry (FLOW-IN)", "begin": "(?=(?>[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ,\\[\\]{}])))", "end": "(?=[,\\[\\]{}])", "name": "meta.flow.map.implicit.yaml", "patterns": [{"include": "#flow-key-plain-in"}, {"match": ":(?=\\[|{)", "name": "invalid.illegal.separator.map.yaml"}, {"include": "#flow-map-value-yaml"}, {"include": "#presentation-detail"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-implicit-entry (FLOW-IN)", "begin": "(?=\"|')", "end": "(?=[,\\[\\]{}])", "name": "meta.flow.map.implicit.yaml", "patterns": [{"include": "#key-double"}, {"include": "source.yaml#key-single"}, {"include": "#flow-map-value-json"}, {"include": "#presentation-detail"}]}]}, "flow-sequence-map-key": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-entry (FLOW-IN)", "patterns": [{"begin": "\\?(?=[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "end": "(?=[,\\[\\]{}])", "beginCaptures": {"0": {"name": "punctuation.definition.map.key.yaml"}}, "name": "meta.flow.map.explicit.yaml", "patterns": [{"include": "#flow-mapping-map-key"}, {"include": "#flow-map-value-yaml"}, {"include": "#flow-map-value-json"}, {"include": "#flow-node"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-implicit-entry (FLOW-IN)", "begin": "(?<=[\t ,\\[{]|^)(?=(?>[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))(?>[^:#,\\[\\]{}]++|:(?![\\x{85 2028 2029}\r\n\t ,\\[\\]{}])|(?<! |\t)#++)*+:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "end": "(?=[,\\[\\]{}])", "name": "meta.flow.map.implicit.yaml", "patterns": [{"include": "#flow-key-plain-in"}, {"match": ":(?=\\[|{)", "name": "invalid.illegal.separator.map.yaml"}, {"include": "#flow-map-value-yaml"}, {"include": "#presentation-detail"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-flow-map-implicit-entry (FLOW-IN)", "begin": "(?=(?>\"(?>[^\\\\\"]++|\\\\.)*+\"|'(?>[^']++|'')*+')[\t ]*+:)", "end": "(?=[,\\[\\]{}])", "name": "meta.flow.map.implicit.yaml", "patterns": [{"include": "#key-double"}, {"include": "source.yaml#key-single"}, {"include": "#flow-map-value-json"}, {"include": "#presentation-detail"}]}]}, "flow-map-value-yaml": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-ns-flow-map-separate-value (FLOW-IN)", "begin": ":(?=[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "end": "(?=[,\\]}])", "beginCaptures": {"0": {"name": "punctuation.separator.map.value.yaml"}}, "name": "meta.flow.pair.value.yaml", "patterns": [{"include": "#flow-node"}]}, "flow-map-value-json": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-ns-flow-map-separate-value (FLOW-IN)", "begin": "(?<=(?>[\"'\\]}]|^)[\t ]*+):", "end": "(?=[,\\]}])", "beginCaptures": {"0": {"name": "punctuation.separator.map.value.yaml"}}, "name": "meta.flow.pair.value.yaml", "patterns": [{"include": "#flow-node"}]}, "flow-plain-in": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-plain-multi-line (FLOW-IN)", "begin": "(?=[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "end": "(?=(?>[\t ]++|\\G)#|[\t ]*+[,\\[\\]{}])", "name": "string.unquoted.plain.in.yaml", "patterns": [{"include": "#tag-implicit-plain-in"}, {"match": "\\G[\t ]++", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[\t ]++$", "name": "punctuation.whitespace.separator.yaml"}, {"match": ":(?=[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "name": "invalid.illegal.multiline-key.yaml"}, {"include": "source.yaml#non-printable"}]}, "flow-key-plain-out": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-plain-one-line (FLOW-OUT)", "begin": "(?=[^-?:,\\[\\]{}#&*!|>'\"%@` \\p{Cntrl}\\p{Surrogate}\\x{FFFE FFFF}]|[?:-](?![\\x{85 2028 2029}\r\n\t ]))", "end": "(?=[\t ]*+:[\\x{85 2028 2029}\r\n\t ]|[\t ]++#)", "name": "meta.map.key.yaml string.unquoted.plain.yaml entity.name.tag.yaml", "patterns": [{"include": "#tag-implicit-plain-out"}, {"match": "\\G[\t ]++", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[\t ]++$", "name": "punctuation.whitespace.separator.yaml"}, {"include": "source.yaml#non-printable"}]}, "flow-key-plain-in": {"comment": "https://yaml.org/spec/1.2.2/#rule-ns-s-implicit-yaml-key (FLOW-KEY)", "begin": "\\G(?![\\x{85 2028 2029}\r\n\t #])", "end": "(?=[\t ]*+(?>:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]|[,\\[\\]{}])|[\t ]++#)", "name": "meta.flow.map.key.yaml string.unquoted.plain.in.yaml entity.name.tag.yaml", "patterns": [{"include": "#tag-implicit-plain-in"}, {"include": "source.yaml#non-printable"}]}, "key-double": {"comment": "https://yaml.org/spec/1.2.2/#double-quoted-style (FLOW-OUT)", "begin": "\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.yaml"}}, "name": "meta.map.key.yaml string.quoted.double.yaml entity.name.tag.yaml", "patterns": [{"match": "[^\t -\\x{10FFFF}]++", "name": "invalid.illegal.character.yaml"}, {"include": "#double-escape"}]}, "double": {"comment": "https://yaml.org/spec/1.2.2/#double-quoted-style", "begin": "\"", "end": "\"", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.yaml"}}, "name": "string.quoted.double.yaml", "patterns": [{"match": "(?<!\")\\G[\t ]++", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[\t ]++$", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[^\\x{85 2028 2029}\r\n\t -\\x{10FFFF}]++", "name": "invalid.illegal.character.yaml"}, {"include": "#double-escape"}]}, "single": {"comment": "https://yaml.org/spec/1.2.2/#single-quoted-style", "begin": "'", "end": "'(?!')", "beginCaptures": {"0": {"name": "punctuation.definition.string.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.string.end.yaml"}}, "name": "string.quoted.single.yaml", "patterns": [{"match": "(?<!')\\G[\t ]++", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[\t ]++$", "name": "punctuation.whitespace.separator.yaml"}, {"match": "[^\\x{85 2028 2029}\r\n\t -\\x{10FFFF}]++", "name": "invalid.illegal.character.yaml"}, {"match": "''", "name": "constant.character.escape.single-quote.yaml"}]}, "double-escape": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-ns-esc-char", "patterns": [{"match": "\\\\[\\x{85 2028 2029}\r\n\t0abtnvfre \"\\\\N_LP]", "name": "constant.character.escape.yaml"}, {"match": "\\\\x[0-9a-fA-F]{2}", "name": "constant.character.escape.unicode.8-bit.yaml"}, {"match": "\\\\u[0-9a-fA-F]{4}", "name": "constant.character.escape.unicode.16-bit.yaml"}, {"match": "\\\\U[0-9a-fA-F]{8}", "name": "constant.character.escape.unicode.32-bit.yaml"}, {"match": "\\\\(?>x[^\"]{2,0}|u[^\"]{4,0}|U[^\"]{8,0}|.)", "name": "invalid.illegal.constant.character.escape.yaml"}]}, "tag-implicit-plain-in": {"comment": "https://yaml.org/type/index.html", "patterns": [{"match": "\\G(?>null|Null|NULL|~)(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.language.null.yaml"}, {"match": "\\G(?>true|True|TRUE|false|False|FALSE|y|Y|yes|Yes|YES|n|N|no|No|NO|on|On|ON|off|Off|OFF)(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.language.boolean.yaml"}, {"match": "\\G[-+]?+(0|[1-9][0-9_]*+)(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.integer.decimal.yaml"}, {"match": "\\G[-+]?+0b[0-1_]++(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.integer.binary.yaml"}, {"match": "\\G[-+]?0[0-7_]++(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.integer.octal.yaml"}, {"match": "\\G[-+]?+0x[0-9a-fA-F_]++(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.integer.hexadecimal.yaml"}, {"match": "\\G[-+]?+[1-9][0-9_]*+(?>:[0-5]?[0-9])++(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.integer.Sexagesimal.yaml"}, {"match": "\\G[-+]?+(?>[0-9][0-9_]*+)?+\\.[0-9.]*+(?>[eE][-+][0-9]+)?+(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.float.decimal.yaml"}, {"match": "\\G[-+]?+[0-9][0-9_]*+(?>:[0-5]?[0-9])++\\.[0-9_]*+(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.float.Sexagesimal.yaml"}, {"match": "\\G[-+]?+\\.(?>inf|Inf|INF)(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.float.inf.yaml"}, {"match": "\\G\\.(?>nan|NaN|NAN)(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.float.nan.yaml"}, {"comment": "https://www.w3.org/TR/NOTE-datetime does not allow spaces, however https://yaml.org/type/timestamp.html does, but the provided regex doesn't match the TZD space in many of the YAML examples", "match": "\\G(?>[0-9]{4}-[0-9]{2,1}-[0-9]{2,1}(?>T|t|[\t ]++)[0-9]{2,1}:[0-9]{2}:[0-9]{2}(?>\\.[0-9]*+)?+[\t ]*+(?>Z|[-+][0-9]{2,1}(?>:[0-9]{2})?+)?+|[0-9]{4}-[0-9]{2}-[0-9]{2})(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.numeric.timestamp.yaml"}, {"match": "\\G<<(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.language.merge.yaml"}, {"match": "\\G=(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.language.value.yaml"}, {"match": "\\G(?>!|&|\\*)(?=[\t ]++#|[\t ]*+(?>[\\x{85 2028 2029}\r\n,\\]}]|:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]))", "name": "constant.language.yaml.yaml"}]}, "tag-implicit-plain-out": {"comment": "https://yaml.org/type/index.html", "patterns": [{"match": "\\G(?>null|Null|NULL|~)(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.language.null.yaml"}, {"match": "\\G(?>true|True|TRUE|false|False|FALSE|yes|Yes|YES|y|Y|no|No|NO|n|N|on|On|ON|off|Off|OFF)(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.language.boolean.yaml"}, {"match": "\\G[-+]?+(0|[1-9][0-9_]*+)(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.integer.decimal.yaml"}, {"match": "\\G[-+]?+0b[0-1_]++(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.integer.binary.yaml"}, {"match": "\\G[-+]?0[0-7_]++(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.integer.octal.yaml"}, {"match": "\\G[-+]?+0x[0-9a-fA-F_]++(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.integer.hexadecimal.yaml"}, {"match": "\\G[-+]?+[1-9][0-9_]*+(?>:[0-5]?[0-9])++(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.integer.Sexagesimal.yaml"}, {"match": "\\G[-+]?+(?>[0-9][0-9_]*+)?+\\.[0-9.]*+(?>[eE][-+][0-9]+)?+(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.float.decimal.yaml"}, {"match": "\\G[-+]?+[0-9][0-9_]*+(?>:[0-5]?[0-9])++\\.[0-9_]*+(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.float.Sexagesimal.yaml"}, {"match": "\\G[-+]?+\\.(?>inf|Inf|INF)(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.float.inf.yaml"}, {"match": "\\G\\.(?>nan|NaN|NAN)(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.float.nan.yaml"}, {"comment": "https://www.w3.org/TR/NOTE-datetime does not allow spaces, however https://yaml.org/type/timestamp.html does, but the provided regex doesn't match the TZD space in many of the YAML examples", "match": "\\G(?>[0-9]{4}-[0-9]{2,1}-[0-9]{2,1}(?>T|t|[\t ]++)[0-9]{2,1}:[0-9]{2}:[0-9]{2}(?>\\.[0-9]*+)?+[\t ]*+(?>Z|[-+][0-9]{2,1}(?>:[0-9]{2})?+)?+|[0-9]{4}-[0-9]{2}-[0-9]{2})(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.numeric.timestamp.yaml"}, {"match": "\\G<<(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.language.merge.yaml"}, {"match": "\\G=(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.language.value.yaml"}, {"match": "\\G(?>!|&|\\*)(?=[\t ]++#|[\t ]*+(?>$|:[\\x{85 2028 2029}\r\n\t ]))", "name": "constant.language.yaml.yaml"}]}, "tag-property": {"comment": "https://yaml.org/spec/1.2.2/#rule-c-ns-tag-property", "//": ["!", "!!", "!<>", "!...", "!!...", "!<...>", "!...!..."], "patterns": [{"match": "!(?=[\\x{85 2028 2029}\r\n\t ])", "name": "storage.type.tag.non-specific.yaml punctuation.definition.tag.non-specific.yaml"}, {"comment": "https://yaml.org/spec/1.2.2/#rule-c-verbatim-tag", "begin": "!<", "end": ">", "beginCaptures": {"0": {"name": "punctuation.definition.tag.begin.yaml"}}, "endCaptures": {"0": {"name": "punctuation.definition.tag.end.yaml"}}, "name": "storage.type.tag.verbatim.yaml", "patterns": [{"match": "%[0-9a-fA-F]{2}", "name": "constant.character.escape.unicode.8-bit.yaml"}, {"match": "%[^\\x{85 2028 2029}\r\n\t ]{2,0}", "name": "invalid.illegal.constant.character.escape.unicode.8-bit.yaml"}, {"include": "source.yaml#non-printable"}, {"match": "[^\\x{85 2028 2029}\r\n\t a-zA-Z0-9-#;/?:@&=+$,_.!~*'()\\[\\]%>]++", "name": "invalid.illegal.unrecognized.yaml"}]}, {"comment": "https://yaml.org/spec/1.2.2/#rule-c-ns-shorthand-tag", "begin": "(?=!)", "end": "(?=[\\x{85 2028 2029}\r\n\t ,\\[\\]{}])", "name": "storage.type.tag.shorthand.yaml", "patterns": [{"comment": "https://yaml.org/spec/1.2.2/#rule-c-secondary-tag-handle", "match": "\\G!!", "name": "punctuation.definition.tag.secondary.yaml"}, {"comment": "https://yaml.org/spec/1.2.2/#rule-c-secondary-tag-handle", "match": "\\G(!)[0-9A-Za-z-]++(!)", "captures": {"1": {"name": "punctuation.definition.tag.named.yaml"}, "2": {"name": "punctuation.definition.tag.named.yaml"}}}, {"comment": "https://yaml.org/spec/1.2.2/#rule-c-primary-tag-handle", "match": "\\G!", "name": "punctuation.definition.tag.primary.yaml"}, {"match": "%[0-9a-fA-F]{2}", "name": "constant.character.escape.unicode.8-bit.yaml"}, {"match": "%[^\\x{85 2028 2029}\r\n\t ]{2,0}", "name": "invalid.illegal.constant.character.escape.unicode.8-bit.yaml"}, {"include": "source.yaml#non-printable"}, {"match": "[^\\x{85 2028 2029}\r\n\t a-zA-Z0-9-#;/?:@&=+$,_.~*'()\\[\\]%]++", "name": "invalid.illegal.unrecognized.yaml"}]}]}, "anchor-property": {"match": "(&)([^ \\p{Cntrl}\\p{Surrogate}\\x{2028 2029 FFFE FFFF}]++)|(&)", "captures": {"0": {"name": "keyword.control.flow.anchor.yaml"}, "1": {"name": "punctuation.definition.anchor.yaml"}, "2": {"name": "variable.other.anchor.yaml"}, "3": {"name": "invalid.illegal.flow.anchor.yaml"}}}, "alias": {"begin": "(\\*)([^ \\p{Cntrl}\\p{Surrogate}\\x{2028 2029 FFFE FFFF}]++)|(\\*)", "end": "(?=:[\\x{85 2028 2029}\r\n\t ,\\[\\]{}]|[,\\[\\]{}])", "captures": {"0": {"name": "keyword.control.flow.alias.yaml"}, "1": {"name": "punctuation.definition.alias.yaml"}, "2": {"name": "variable.other.alias.yaml"}, "3": {"name": "invalid.illegal.flow.alias.yaml"}}, "patterns": [{"include": "#presentation-detail"}]}, "byte-order-mark": {"comment": "﻿", "begin": "\\G", "while": "\\G(?=[\\x{FEFF 85 2028 2029}\r\n\t ])", "patterns": [{"begin": "(?=#)", "while": "\\G", "patterns": [{"include": "#presentation-detail"}]}, {"begin": "\\G\\x{FEFF}", "while": "\\G", "beginCaptures": {"0": {"name": "byte-order-mark.yaml"}}, "patterns": [{"include": "#presentation-detail"}]}, {"include": "#presentation-detail"}]}, "presentation-detail": {"patterns": [{"match": "[\t ]++", "name": "punctuation.whitespace.separator.yaml"}, {"comment": "https://yaml.org/spec/1.1/#id871136", "match": "[\\x{85 2028 2029}\r\n]++", "name": "punctuation.separator.line-break.yaml"}, {"include": "source.yaml#non-printable"}, {"include": "#comment"}, {"include": "#unknown"}]}, "comment": {"comment": "Comments must be separated from other tokens by white space characters. `space`, `newline` or `carriage-return`. `#(.*)` causes performance issues", "begin": "(?<=^|[\\x{FEFF 85 2028 2029} ])#", "end": "[\\x{85 2028 2029}\r\n]", "captures": {"0": {"name": "punctuation.definition.comment.yaml"}}, "name": "comment.line.number-sign.yaml", "patterns": [{"include": "source.yaml#non-printable"}]}, "unknown": {"match": ".[[^\\x{85 2028 2029}#\"':,\\[\\]{}]&&!-~\\x{A0}-\\x{D7FF}\\x{E000}-\\x{FFFD}\\x{010000}-\\x{10FFFF}]*+", "name": "invalid.illegal.unrecognized.yaml"}}}