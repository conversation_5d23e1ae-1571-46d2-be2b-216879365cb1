{"name": "php-language-features", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "icon": "icons/logo.png", "engines": {"vscode": "0.10.x"}, "activationEvents": ["onLanguage:php"], "main": "./dist/phpMain", "categories": ["Programming Languages"], "capabilities": {"virtualWorkspaces": false, "untrustedWorkspaces": {"supported": "limited", "description": "%workspaceTrust%", "restrictedConfigurations": ["php.validate.executablePath"]}}, "contributes": {"configuration": {"title": "%configuration.title%", "type": "object", "order": 20, "properties": {"php.suggest.basic": {"type": "boolean", "default": true, "description": "%configuration.suggest.basic%"}, "php.validate.enable": {"type": "boolean", "default": true, "description": "%configuration.validate.enable%"}, "php.validate.executablePath": {"type": ["string", "null"], "default": null, "description": "%configuration.validate.executablePath%", "scope": "machine-overridable"}, "php.validate.run": {"type": "string", "enum": ["onSave", "onType"], "default": "onSave", "description": "%configuration.validate.run%"}}}, "jsonValidation": [{"fileMatch": "composer.json", "url": "https://getcomposer.org/schema.json"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}