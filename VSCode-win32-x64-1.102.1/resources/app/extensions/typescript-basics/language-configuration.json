{"comments": {"lineComment": "//", "blockComment": ["/*", "*/"]}, "brackets": [["${", "}"], ["{", "}"], ["[", "]"], ["(", ")"]], "autoClosingPairs": [{"open": "{", "close": "}"}, {"open": "[", "close": "]"}, {"open": "(", "close": ")"}, {"open": "'", "close": "'", "notIn": ["string", "comment"]}, {"open": "\"", "close": "\"", "notIn": ["string"]}, {"open": "`", "close": "`", "notIn": ["string", "comment"]}, {"open": "/**", "close": " */", "notIn": ["string"]}], "surroundingPairs": [["{", "}"], ["[", "]"], ["(", ")"], ["'", "'"], ["\"", "\""], ["`", "`"], ["<", ">"]], "colorizedBracketPairs": [["(", ")"], ["[", "]"], ["{", "}"], ["<", ">"]], "autoCloseBefore": ";:.,=}])>` \n\t", "folding": {"markers": {"start": "^\\s*//\\s*#?region\\b", "end": "^\\s*//\\s*#?endregion\\b"}}, "wordPattern": {"pattern": "(-?\\d*\\.\\d\\w*)|([^\\`\\@\\~\\!\\%\\^\\&\\*\\(\\)\\-\\=\\+\\[\\{\\]\\}\\\\\\|\\;\\:\\'\\\"\\,\\.\\<\\>/\\?\\s]+)"}, "indentationRules": {"decreaseIndentPattern": {"pattern": "^\\s*[\\}\\]\\)].*$"}, "increaseIndentPattern": {"pattern": "^.*(\\{[^}]*|\\([^)]*|\\[[^\\]]*)$"}, "unIndentedLinePattern": {"pattern": "^(\\t|[ ])*[ ]\\*[^/]*\\*/\\s*$|^(\\t|[ ])*[ ]\\*/\\s*$|^(\\t|[ ])*\\*([ ]([^\\*]|\\*(?!/))*)?$"}, "indentNextLinePattern": {"pattern": "^((.*=>\\s*)|((.*[^\\w]+|\\s*)((if|while|for)\\s*\\(.*\\)\\s*|else\\s*)))$"}}, "onEnterRules": [{"beforeText": {"pattern": "^\\s*/\\*\\*(?!/)([^\\*]|\\*(?!/))*$"}, "afterText": {"pattern": "^\\s*\\*/$"}, "action": {"indent": "indentOutdent", "appendText": " * "}}, {"beforeText": {"pattern": "^\\s*/\\*\\*(?!/)([^\\*]|\\*(?!/))*$"}, "action": {"indent": "none", "appendText": " * "}}, {"beforeText": {"pattern": "^(\\t|[ ])*\\*([ ]([^\\*]|\\*(?!/))*)?$"}, "previousLineText": {"pattern": "(?=^(\\s*(/\\*\\*|\\*)).*)(?=(?!(\\s*\\*/)))"}, "action": {"indent": "none", "appendText": "* "}}, {"beforeText": {"pattern": "^(\\t|[ ])*[ ]\\*/\\s*$"}, "action": {"indent": "none", "removeText": 1}}, {"beforeText": {"pattern": "^(\\t|[ ])*[ ]\\*[^/]*\\*/\\s*$"}, "action": {"indent": "none", "removeText": 1}}, {"beforeText": {"pattern": "^\\s*(\\bcase\\s.+:|\\bdefault:)$"}, "afterText": {"pattern": "^(?!\\s*(\\bcase\\b|\\bdefault\\b))"}, "action": {"indent": "indent"}}, {"previousLineText": "^\\s*(((else ?)?if|for|while)\\s*\\(.*\\)\\s*|else\\s*)$", "beforeText": "^\\s+([^{i\\s]|i(?!f\\b))", "action": {"indent": "outdent"}}, {"beforeText": "^.*\\([^\\)]*$", "afterText": "^\\s*\\).*$", "action": {"indent": "indentOutdent", "appendText": "\t"}}, {"beforeText": "^.*\\{[^\\}]*$", "afterText": "^\\s*\\}.*$", "action": {"indent": "indentOutdent", "appendText": "\t"}}, {"beforeText": "^.*\\[[^\\]]*$", "afterText": "^\\s*\\].*$", "action": {"indent": "indentOutdent", "appendText": "\t"}}, {"beforeText": "(?<!\\\\|\\w:)//\\s*\\S", "afterText": "^(?!\\s*$).+", "action": {"indent": "none", "appendText": "// "}}]}