"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function formatFixed(whole, fraction) {
    return whole + fraction / 2 ** 16;
}
exports.formatFixed = formatFixed;
function formatLongDateTime(high, low) {
    // OpenType dates are since 1904. We make them since 1970 to align with unix
    // and multiply by 1000 to make it a millisecond time like the rest of
    // Javascript
    return ((high * 2 ** 32) + low - 2082844800) * 1000;
}
exports.formatLongDateTime = formatLongDateTime;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/7adae6a56e34cb64d08899664b814cf620465925/node_modules/font-finder/dist/tables/utility.js.map