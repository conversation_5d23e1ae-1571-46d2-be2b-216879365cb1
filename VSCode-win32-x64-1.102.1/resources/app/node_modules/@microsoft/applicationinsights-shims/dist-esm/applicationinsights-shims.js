// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.
export { strShimFunction, strShimObject, strShimUndefined, strShimPrototype, strShimHasOwnProperty, strDefault, ObjClass, Obj<PERSON>roto, Obj<PERSON>sign, Obj<PERSON><PERSON>, ObjDefineProperty, ObjHasOwnProperty } from "./Constants";
export { throwTypeError, objCreateFn, getGlobal } from "./Helpers";
export { __assignFn, __extendsFn, __restFn, __spreadArrayFn, __spreadArraysFn, __decorateFn, __paramFn, __metadataFn, __createBindingFn, __valuesFn, __readFn, __makeTemplateObjectFn, __importDefaultFn, __importStarFn, __exportStarFn } from "./TsLibShims";
export { __exposeGlobalTsLib } from "./TsLibGlobals";
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/7adae6a56e34cb64d08899664b814cf620465925/node_modules/@microsoft/applicationinsights-shims/dist-esm/applicationinsights-shims.js.map