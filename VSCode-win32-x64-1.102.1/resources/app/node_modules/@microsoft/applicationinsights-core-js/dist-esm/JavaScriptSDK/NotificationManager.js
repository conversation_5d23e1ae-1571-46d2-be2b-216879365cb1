/*
 * Application Insights JavaScript SDK - Core, 2.8.15
 * Copyright (c) Microsoft and contributors. All rights reserved.
 */


import dynamicProto from "@microsoft/dynamicproto-js";
import { _DYN_ADD_NOTIFICATION_LIS1, _DYN_IS_ASYNC, _DYN_IS_CHILD_EVT, _DYN_LISTENERS, _DYN_PUSH, _DYN_REMOVE_NOTIFICATION_2, _DYN_SPLICE } from "../__DynamicConstants";
import { arrForEach, arrIndexOf } from "./HelperFuncs";
import { STR_EVENTS_DISCARDED, STR_EVENTS_SEND_REQUEST, STR_EVENTS_SENT, STR_PERF_EVENT } from "./InternalConstants";
function _runListeners(listeners, name, isAsync, callback) {
    arrForEach(listeners, function (listener) {
        if (listener && listener[name]) {
            if (isAsync) {
                setTimeout(function () { return callback(listener); }, 0);
            }
            else {
                try {
                    callback(listener);
                }
                catch (e) {
                    // Catch errors to ensure we don't block sending the requests
                }
            }
        }
    });
}
/**
 * Class to manage sending notifications to all the listeners.
 */
var NotificationManager = /** @class */ (function () {
    function NotificationManager(config) {
        this.listeners = [];
        var perfEvtsSendAll = !!(config || {}).perfEvtsSendAll;
        dynamicProto(NotificationManager, this, function (_self) {
            _self[_DYN_ADD_NOTIFICATION_LIS1 /* @min:%2eaddNotificationListener */] = function (listener) {
                _self.listeners[_DYN_PUSH /* @min:%2epush */](listener);
            };
            /**
             * Removes all instances of the listener.
             * @param {INotificationListener} listener - AWTNotificationListener to remove.
             */
            _self[_DYN_REMOVE_NOTIFICATION_2 /* @min:%2eremoveNotificationListener */] = function (listener) {
                var index = arrIndexOf(_self[_DYN_LISTENERS /* @min:%2elisteners */], listener);
                while (index > -1) {
                    _self.listeners[_DYN_SPLICE /* @min:%2esplice */](index, 1);
                    index = arrIndexOf(_self[_DYN_LISTENERS /* @min:%2elisteners */], listener);
                }
            };
            /**
             * Notification for events sent.
             * @param {ITelemetryItem[]} events - The array of events that have been sent.
             */
            _self[STR_EVENTS_SENT /* @min:%2eeventsSent */] = function (events) {
                _runListeners(_self[_DYN_LISTENERS /* @min:%2elisteners */], STR_EVENTS_SENT, true, function (listener) {
                    listener[STR_EVENTS_SENT /* @min:%2eeventsSent */](events);
                });
            };
            /**
             * Notification for events being discarded.
             * @param {ITelemetryItem[]} events - The array of events that have been discarded by the SDK.
             * @param {number} reason           - The reason for which the SDK discarded the events. The EventsDiscardedReason
             * constant should be used to check the different values.
             */
            _self[STR_EVENTS_DISCARDED /* @min:%2eeventsDiscarded */] = function (events, reason) {
                _runListeners(_self[_DYN_LISTENERS /* @min:%2elisteners */], STR_EVENTS_DISCARDED, true, function (listener) {
                    listener[STR_EVENTS_DISCARDED /* @min:%2eeventsDiscarded */](events, reason);
                });
            };
            /**
             * [Optional] A function called when the events have been requested to be sent to the sever.
             * @param {number} sendReason - The reason why the event batch is being sent.
             * @param {boolean} isAsync   - A flag which identifies whether the requests are being sent in an async or sync manner.
             */
            _self[STR_EVENTS_SEND_REQUEST /* @min:%2eeventsSendRequest */] = function (sendReason, isAsync) {
                _runListeners(_self[_DYN_LISTENERS /* @min:%2elisteners */], STR_EVENTS_SEND_REQUEST, isAsync, function (listener) {
                    listener[STR_EVENTS_SEND_REQUEST /* @min:%2eeventsSendRequest */](sendReason, isAsync);
                });
            };
            _self[STR_PERF_EVENT /* @min:%2eperfEvent */] = function (perfEvent) {
                if (perfEvent) {
                    // Send all events or only parent events
                    if (perfEvtsSendAll || !perfEvent[_DYN_IS_CHILD_EVT /* @min:%2eisChildEvt */]()) {
                        _runListeners(_self[_DYN_LISTENERS /* @min:%2elisteners */], STR_PERF_EVENT, false, function (listener) {
                            if (perfEvent[_DYN_IS_ASYNC /* @min:%2eisAsync */]) {
                                setTimeout(function () { return listener[STR_PERF_EVENT /* @min:%2eperfEvent */](perfEvent); }, 0);
                            }
                            else {
                                listener[STR_PERF_EVENT /* @min:%2eperfEvent */](perfEvent);
                            }
                        });
                    }
                }
            };
        });
    }
// Removed Stub for NotificationManager.prototype.addNotificationListener.
// Removed Stub for NotificationManager.prototype.removeNotificationListener.
// Removed Stub for NotificationManager.prototype.eventsSent.
// Removed Stub for NotificationManager.prototype.eventsDiscarded.
// Removed Stub for NotificationManager.prototype.eventsSendRequest.
    // This is a workaround for an IE8 bug when using dynamicProto() with classes that don't have any
    // non-dynamic functions or static properties/functions when using uglify-js to minify the resulting code.
    // this will be removed when ES3 support is dropped.
    NotificationManager.__ieDyn=1;

    return NotificationManager;
}());
export { NotificationManager };
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/7adae6a56e34cb64d08899664b814cf620465925/node_modules/@microsoft/applicationinsights-core-js/dist-esm/JavaScriptSDK/NotificationManager.js.map