{"name": "deep-extend", "description": "Recursive object extending", "license": "MIT", "version": "0.6.0", "homepage": "https://github.com/unclechu/node-deep-extend", "keywords": ["deep-extend", "extend", "deep", "recursive", "xtend", "clone", "merge", "json"], "licenses": [{"type": "MIT", "url": "https://raw.githubusercontent.com/unclechu/node-deep-extend/master/LICENSE"}], "repository": {"type": "git", "url": "git://github.com/unclechu/node-deep-extend.git"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "bugs": "https://github.com/unclechu/node-deep-extend/issues", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/rprieto"}, {"name": "<PERSON>", "url": "https://github.com/maxmaximov"}, {"name": "<PERSON>", "url": "https://github.com/maxdeviant"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwakerman"}], "main": "lib/deep-extend.js", "engines": {"node": ">=4.0.0"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "devDependencies": {"mocha": "5.2.0", "should": "13.2.1"}, "files": ["index.js", "lib/"]}