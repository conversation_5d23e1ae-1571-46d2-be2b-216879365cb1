{"author": "<PERSON> <<EMAIL>>", "name": "buffer-crc32", "description": "A pure javascript CRC32 algorithm that plays nice with binary data", "version": "0.2.13", "licenses": [{"type": "MIT", "url": "https://github.com/brianloveswords/buffer-crc32/raw/master/LICENSE"}], "contributors": [{"name": "<PERSON>", "github": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/brianloveswords/buffer-crc32", "repository": {"type": "git", "url": "git://github.com/brianloveswords/buffer-crc32.git"}, "main": "index.js", "scripts": {"test": "./node_modules/.bin/tap tests/*.test.js"}, "dependencies": {}, "devDependencies": {"tap": "~0.2.5"}, "optionalDependencies": {}, "engines": {"node": "*"}, "license": "MIT", "files": ["index.js"]}