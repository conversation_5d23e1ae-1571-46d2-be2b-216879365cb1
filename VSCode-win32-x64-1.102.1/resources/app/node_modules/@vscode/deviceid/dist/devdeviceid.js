"use strict";
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See LICENSE in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDeviceId = void 0;
const uuid_1 = require("uuid");
const storage = __importStar(require("./storage.js"));
async function getDeviceId() {
    let deviceId;
    try {
        deviceId = await storage.getDeviceId();
    }
    catch (e) {
        // Regenerate the deviceId if it cannot be read
    }
    if (deviceId) {
        return deviceId;
    }
    else {
        const newDeviceId = (0, uuid_1.v4)().toLowerCase();
        await storage.setDeviceId(newDeviceId);
        return newDeviceId;
    }
}
exports.getDeviceId = getDeviceId;//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/7adae6a56e34cb64d08899664b814cf620465925/node_modules/@vscode/deviceid/dist/devdeviceid.js.map