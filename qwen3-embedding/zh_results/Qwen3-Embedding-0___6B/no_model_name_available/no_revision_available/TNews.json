{"dataset_revision": "317f262bf1e6126357bbe89e875451e4b0938fe4", "task_name": "TNews", "mteb_version": "1.38.34", "scores": {"validation": [{"accuracy": 0.50813, "f1": 0.488206, "f1_weighted": 0.507917, "scores_per_experiment": [{"accuracy": 0.5148, "f1": 0.494886, "f1_weighted": 0.516914}, {"accuracy": 0.5026, "f1": 0.483566, "f1_weighted": 0.501212}, {"accuracy": 0.5107, "f1": 0.49048, "f1_weighted": 0.51156}, {"accuracy": 0.5102, "f1": 0.491636, "f1_weighted": 0.509004}, {"accuracy": 0.5123, "f1": 0.493156, "f1_weighted": 0.515841}, {"accuracy": 0.5118, "f1": 0.492929, "f1_weighted": 0.508908}, {"accuracy": 0.4939, "f1": 0.471464, "f1_weighted": 0.492252}, {"accuracy": 0.5064, "f1": 0.4839, "f1_weighted": 0.50223}, {"accuracy": 0.4998, "f1": 0.479649, "f1_weighted": 0.499499}, {"accuracy": 0.5188, "f1": 0.500388, "f1_weighted": 0.521755}], "main_score": 0.50813, "hf_subset": "default", "languages": ["cmn-Hans"]}]}, "evaluation_time": 1460.1306314468384, "kg_co2_emissions": null}