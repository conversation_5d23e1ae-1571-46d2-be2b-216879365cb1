import mteb
from sentence_transformers import SentenceTransformer
import os
model_name = r"E:\qwen3-embedding\Qwen3-Embedding-0___6B"
output_dir_name = os.path.basename(model_name)
output_folder = os.path.join("zh_results",output_dir_name)

model = SentenceTransformer(model_name)
tasks = mteb.get_tasks(tasks=["TNews"]) 
evaluation = mteb.MTEB(tasks=tasks)
results = evaluation.run(model, output_folder=output_folder)



