---
license: apache-2.0
language:
- en
dataset_info:
  features:
  - name: problem
    dtype: large_string
  - name: solution
    dtype: large_string
  - name: answer
    dtype: large_string
  - name: problem_type
    dtype: large_string
  - name: question_type
    dtype: large_string
  - name: problem_is_valid
    dtype: large_string
  - name: solution_is_valid
    dtype: large_string
  - name: source
    dtype: large_string
  - name: synthetic
    dtype: bool
  - name: generations
    large_list: large_string
  - name: generations_count
    dtype: int64
  - name: correctness
    struct:
    - name: llama_verification
      sequence: bool
    - name: math_verify_answer
      sequence: bool
    - name: math_verify_reparsed_answer
      sequence: bool
  - name: reparsed_answers
    sequence: string
  splits:
  - name: train
    num_bytes: 19180397882
    num_examples: 516499
  download_size: **********
  dataset_size: 19180397882
configs:
- config_name: default
  data_files:
  - split: train
    path: data/train-*
---


# OpenR1-Math-Raw

## Dataset description
OpenR1-Math-Raw is a large-scale dataset for mathematical reasoning. It consists of 516k math problems sourced from [AI-MO/NuminaMath-1.5](https://huggingface.co/datasets/AI-MO/NuminaMath-1.5) with 1 to 8 reasoning traces generated by [DeepSeek R1](https://huggingface.co/deepseek-ai/DeepSeek-R1). 
The traces were verified using [Math Verify](https://github.com/huggingface/Math-Verify) and LLM-as-Judge based verifier (Llama-3.3-70B-Instruct)

The dataset contains:
- `516,499` problems
- `1,209,403` R1-generated solutions, with 2.3 solutions per problem on average
- re-parsed answers (`reparsed_answers`) using Llama-3.3-70B-Instruct



With following distribution of correct answers:
| Metric                           | Correct - Generation | Total - Generation | Correct - Problem | Total - Problem |
|----------------------------------|----------------------|--------------------|-------------------|-----------------|
| Math Verify Reparsed Answer      | 679,358              | 944,106            | 266,654           | 376,956         |
| LLaMA Verification               | 602,766              | 944,106            | 308,391           | 376,956         |
| Math Verify Answer               | 613,535              | 944,106            | 238,951           | 376,956         |


You can load the dataset as follows:

```python
from datasets import load_dataset

ds = load_dataset("open-r1/OpenR1-Math-Raw", split="train")
```

## Dataset curation

We only keep the solutions that fit in the 16k-token budget, and follow the `<think>...</think>` reasoning format.
Only the non-synthetic problems from NuminaMath-1.5 were used.

For a more curated sample of this dataset and more details please see [open-r1/OpenR1-Math-220k](https://huggingface.co/datasets/open-r1/OpenR1-Math-220k).


## Changelog
### [1.1]
- Added `reparsed_answers` column, created by meta-llama/Meta-Llama-3.3-70B-Instruct prompted to extract answer from $solution$ column.
- Added `correctness` column, which contains verification results computed by math-verify on the reparsed_answers/answer column, as well as llama-based verification using meta-llama/Meta-Llama-3.3-70B-Instruct

## License
The dataset is licensed under Apache 2.0

