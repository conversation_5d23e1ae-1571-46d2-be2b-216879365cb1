---
license: apache-2.0
language:
- en
tags:
- curator
- synthetic
---

<p align="center">
    <a href="https://bespokelabs.ai"><img src="Bespoke-Labs-Logo-on-Mint.png" width="550"></a>
</p>

## Bespoke-Stratos-17k

[We](https://bespokelabs.ai) replicated and improved the [Berkeley Sky-T1](https://novasky-ai.github.io/posts/sky-t1/) data pipeline using SFT distillation data 
from [DeepSeek-R1](https://github.com/deepseek-ai/DeepSeek-R1) to create Bespoke-Stratos-17k -- a reasoning dataset of questions, reasoning traces, and answers. 

This data was used to train:
1. [Bespoke-Stratos-32B](https://huggingface.co/bespokelabs/Bespoke-Stratos-32B), a 32B reasoning model which is a fine-tune of [Qwen-2.5-32B-Instruct](https://huggingface.co/Qwen/Qwen2.5-32B-Instruct)
2. [Bespoke-Stratos-7B](https://huggingface.co/bespokelabs/Bespoke-Stratos-7B), a 7B reasoning model which is a fine-tune of [Qwen-2.5-7B-Instruct](https://huggingface.co/Qwen/Qwen2.5-7B-Instruct).

<a href="https://github.com/bespokelabsai/curator/">
 <img src="https://huggingface.co/datasets/bespokelabs/Bespoke-Stratos-17k/resolve/main/made_with_curator.png" alt="Made with Curator" width=200px>
</a>

## Metrics for Bespoke-Stratos-32B
| Metric | Bespoke-Stratos-32B | Sky-T1-32B | o1-preview | DeepSeek-R1 | DeepSeek-R1-Distill-Qwen-32B (Ours)|DeepSeek-R1-Distill-Qwen-32B (Reported)|
|---|---|---|---|---|---|---|
| AIME2024 | 63.3 | 43.3 | 40.0 | 79.8 | 66.7 | 72.6 |
| MATH500 | 93.0 | 82.4 | 81.4 | 97.3 | 89.8 | 94.3 |
| GPQA-Diamond | 58.1 | 56.8 | 75.2 | 71.5 | 61.1 | 62.1 |
| LCB v2 Easy | 96.7 | 86.3 | 92.9 | - | 91.2 | - |
| LCB v2 Medium | 75.2 | 56.8 | 54.9 | - | 75.7 | - |
| LCB v2 Hard | 26.2 | 17.9 | 16.3 | - | 38.2 | - |
| LCB v2 All | 71.1 | 57.9 | 59.1 | - | 72.2 | - |

## Metrics for Bespoke-Stratos-7B

||Bespoke-Stratos-7B|Qwen2.5-7B-Instruct|DeepSeek-R1-Distill-Qwen-7B (Ours)|DeepSeek-R1-Distill-Qwen-7B (Reported)|
|---|---|---|---|---|
|AIME2024|20.0|10.0|43.3|55.5|
|MATH500|82.0|74.2|89.4|92.8|
|GPQA-Diamond|37.8|33.3|44.9|49.1|
|LiveCodeBench v2 Easy|71.4|65.9|81.3|-|
|LiveCodeBench v2 Medium|25.5|18.9|42.2|-|
|LiveCodeBench v2 Hard|1.6|3.3|2.4|-|
|LiveCodeBench v2 All|36.1|31.9|46.6|-|

## Details
The code for curating the data is [here](https://github.com/bespokelabsai/curator/tree/main/examples/bespoke-stratos-data-generation).
Please also refer to [Sky-T1’s codebase](https://github.com/NovaSky-AI/SkyThought) for the training and evaluation code. 

Similarly to [Sky-T1_data_17k](https://huggingface.co/datasets/NovaSky-AI/Sky-T1_data_17k), this dataset contains 5k coding data from APPs and TACO, and 10k math data from AIME, MATH, and Olympiads subsets of the NuminaMATH dataset, and 1k science and puzzle data from STILL-2. Note that the exact problems included may differ due to the rejection sampling process.

We used Bespoke Curator to create the synthetic reasoning dataset. We ported the Sky-T1 data pipeline into Curator, which helped us generate the reasoning dataset within 1.5 hours with DeepSeek-R1 at a cost of $800 without hiccups.

Rejection sampling involves filtering out reasoning traces with incorrect solutions. This is challenging for code verification, which we speed up using a Ray cluster. We are currently integrating code execution verifier directly in Curator, so stay tuned.

We followed the same recipe as the Sky-T1, but with the following differences:

- We used DeepSeek-R1 as the teacher reasoning model instead of QwQ.
- The Sky-T1 recipe used gpt-4o-mini to reformat QwQ’s traces, whereas we did not reformat DeepSeek-R1’s. We found that DeepSeek-R1’s reasoning traces were sufficiently well-formatted and coherent for parsing and finetuning even without an intermediate reformatting step.
- We used gpt-4o-mini instead of Sky-T1’s parsing logic to filter out incorrect math solutions. Using gpt-4o-mini allowed us to reduce the number of false negatives, increasing the number of retained correct solutions from 25% to 73%.

## Citation

```bibtex
@misc{bespoke_stratos,  
    author = {Bespoke Labs},  
    title = {Bespoke-Stratos: The unreasonable effectiveness of reasoning distillation},  
    howpublished = {https://www.bespokelabs.ai/blog/bespoke-stratos-the-unreasonable-effectiveness-of-reasoning-distillation},  
    note = {Accessed: 2025-01-22},  
    year = {2025}
}
```
## Acknowledgement

We are standing on the shoulders of giants. [Bespoke Labs](https://bespokelabs.ai) would like to thank [Berkeley Sky Computing Lab](https://sky.cs.berkeley.edu/) for their work on [Sky-T1](https://novasky-ai.github.io/posts/sky-t1/) and for releasing the [code](https://github.com/NovaSky-AI/SkyThought) and [data](https://github.com/NovaSky-AI/SkyThought), [Deepseek](https://www.google.com/search?q=deepseek&oq=deepseek&gs_lcrp=EgZjaHJvbWUyDwgAEEUYORiDARixAxiABDIGCAEQRRg8Mg8IAhBFGDsYgwEYsQMYgAQyDQgDEAAYgwEYsQMYgAQyDQgEEAAYgwEYsQMYgAQyBggFEEUYPDIGCAYQRRg8MgYIBxBFGDzSAQg1MTE3ajBqN6gCALACAA&sourceid=chrome&ie=UTF-8) for releasing the [Deepseek-R1](https://github.com/deepseek-ai/DeepSeek-R1) [model](https://huggingface.co/deepseek-ai/DeepSeek-R1), and the [Datacomp](https://datacomp.ai/) community for insightful discussions.


To be in the loop, please sign up to be notified at https://bespokelabs.ai/newsletter