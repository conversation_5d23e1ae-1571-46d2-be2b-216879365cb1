---
{vezora license}
---
<img src="https://huggingface.co/Vezora/Agent-7b-v1/resolve/main/Designer.png" width="200" height="200" />

# Open-Critic-GPT Dataset

## Overview
**Creator** [<PERSON>](https://twitter.com/mejia_petit)
[My Ko<PERSON>](https://ko-fi.com/nicolasmejiapetit)

The Open-Critic-GPT dataset is a synthetic dataset created to train models in both identifying and fixing bugs in code. The dataset is generated using a unique synthetic data pipeline which involves:

1. Prompting a local model with an existing code example.
2. Introducing bugs into the code. While also having the model, from a first-person perspective, find the bugs and explain them.
3. Manipulating the data by shifting around where the broken code and working code is, and removing the # bug// and # error// comments from the code.

This process allows the creation of two distinct datasets within Open-Critic-GPT:

- **Code-Preference-Pairs Dataset**: (SFT) Contains pairs of duplicate code examples, with the only difference being one the rejected example has the bugged code 'surgically transplanted in' while the accepted is left the same.
- **Open-Critic-GPT Dataset**: (DPO) Trains the model to find bugs and produce working code from broken code.
- Both dataset's spans a total of 127 different language/structures, (some may have been lost in conversion started with 122k ended with 55k, due to lack of structured output, a finetuned model may preform better structured outputs.)
- Both datasets contain of ~55K examples each (which both come from the same parent example)

## Dataset Structure

The dataset is organized as follows:

- **Code Examples**: Each code example consists of a a given snippet of bugged code and asked to find the bugs and fix them:
  - **Bugged Code**: The version of the code with introduced bugs and no comments, to avoid the model from learning from comments that say "Bug" or "Error".
  - **Explanation**: Explanation are provided for each bugged code example, detailing the nature of the bug, what the bug does to the code, and tips to avoid it.
  - **Fixed Code**: Lastly the model write the fully working code, with bugs fixed and comments added to the code.
  

## Usage
 - Just give me credit :)
 - Oh and current employee's of 'Open AI' and or the company as a whole is NOT permitted use this dataset or any derivative work that may come for training. It is mentioned in the custom apache license.
 - Otherwise to everyone else, it falls under Apache 2.0 :).
### Training Models

When training models with the Open-Critic-GPT dataset, it is essential to use a data collator to ensure that the loss is not calculated on the bugged code. The data collator manages the dataset during training to provide the model with the correct inputs and outputs for loss calculation.


### Crediting dataset creators:
- This dataset was created using 'm-a-p/CodeFeedback-Filtered-Instruction' Which contiains data from several different sources
- Here are the orginal authors of the oringal sources, Thank you to the following authors: Nick Roshdieh for evol Instruct, Ajinkya Bawase for Python shareGPT 23k, Intellligent Software Engineering for Magicoder, and Multimodal Art Projection for the compiled and filtered m-a-p/CodeFeedback-Filtered-Instruction.

### Begging for money section.
- I created this dataset off a single 3090. Imagine what I could do with two. 
- I can't continue to work on these open source projects, with out receiving a sponsorship or a form of compensation, all the money I make from this will go dirrectly back into helping the open source community.
- If you can, It would mean the world to me any donation helps me release this work for free. thank you :)
- [Kofi](https://ko-fi.com/nicolasmejiapetit)